2025-08-17 12:29:03.053 [main] INFO  org.eu.ump90.tradej.TradejApplication - Starting TradejApplication using Java 21.0.7 with PID 4984 (C:\Users\<USER>\Documents\tradej\target\classes started by ump90 in C:\Users\<USER>\Documents\tradej)
2025-08-17 12:29:03.053 [main] DEBUG org.eu.ump90.tradej.TradejApplication - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-08-17 12:29:03.057 [main] INFO  org.eu.ump90.tradej.TradejApplication - The following 1 profile is active: "dev"
2025-08-17 12:29:03.628 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [C:\Users\<USER>\Documents\tradej\target\classes\org\eu\ump90\tradej\mapper\KlineMapper.class]
2025-08-17 12:29:03.629 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'klineMapper' and 'org.eu.ump90.tradej.mapper.KlineMapper' mapperInterface
2025-08-17 12:29:03.631 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'klineMapper'.
2025-08-17 12:29:03.861 [main] DEBUG com.dtflys.forest.scanner.ClassPathClientScanner - Identified candidate component class: file [C:\Users\<USER>\Documents\tradej\target\classes\org\eu\ump90\tradej\api\BinanceAPI.class]
2025-08-17 12:29:03.862 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner - [Forest] Created Forest Client Bean with name 'binanceAPI' and Proxy of 'org.eu.ump90.tradej.api.BinanceAPI' client interface
2025-08-17 12:29:04.046 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-17 12:29:04.058 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-17 12:29:04.060 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 12:29:04.061 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-17 12:29:04.123 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 12:29:04.124 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1029 ms
2025-08-17 12:29:04.236 [main] DEBUG org.mybatis.spring.SqlSessionFactoryBean - Property 'mapperLocations' was not specified.
2025-08-17 12:29:04.459 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-17 12:29:04.487 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-08-17 12:29:04.502 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-17 12:29:04.551 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-17 12:29:04.879 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-17 12:29:04.886 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-08-17 12:29:04.896 [main] INFO  org.eu.ump90.tradej.TradejApplication - Started TradejApplication in 2.316 seconds (process running for 2.594)
2025-08-17 12:29:23.921 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-17 12:29:23.927 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
