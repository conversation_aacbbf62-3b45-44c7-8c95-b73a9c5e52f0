spring:
  application:
    name: tradej
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************
    username: root
    password: asswecan
  ai:
    openai:
      api-key: AIzaSyAcEWBGeoyr8m_D5_HPLmJOGOlLQtDFo-g
      base-url: https://generativelanguage.googleapis.com/v1beta/openai/
      chat:
        completions-path: /chat/completions
  # 日志配置
  profiles:
    active: dev  # 默认激活开发环境配置

# 日志配置
logging:
  config: classpath:logback-spring.xml  # 指定日志配置文件

forest:
  max-connections: 1000        # 连接池最大连接数
  connect-timeout: 3000        # 连接超时时间，单位为毫秒
  read-timeout: 3000           # 读取超时时间，单位为毫秒
  log-enabled: true            # 启用Forest日志
  log-request: true            # 记录请求日志
  log-response-status: true    # 记录响应状态日志
  log-response-content: true   # 记录响应内容日志

# MyBatis配置
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 使用SLF4J作为MyBatis日志实现
    map-underscore-to-camel-case: true  # 开启驼峰命名转换